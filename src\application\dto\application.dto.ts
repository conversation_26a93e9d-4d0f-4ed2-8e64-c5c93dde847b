/**
 * Simplified Application DTOs
 *
 * Clean, maintainable data transfer objects for application management.
 * Removed complex fields and validations for better maintainability.
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsDateString,
  IsNumber,
  IsInt,
  IsArray,
  IsObject,
  IsBoolean,
  Min,
  Max,
  ArrayNotEmpty,
} from 'class-validator';
import { Transform } from 'class-transformer';
import {
  ApplicationStatus,
  PriorityLevel,
  WorkflowStepStatus,
} from '@prisma/client';

/**
 * DTO for creating a new application
 */
export class CreateApplicationDto {
  // REMOVED: application_type field - following non-destructive patterns
  // @ApiProperty({
  //   description: 'Type of application (service, package, training, consulting)',
  // })
  // @IsString()
  // application_type: string;

  @ApiProperty({ description: 'Service type identifier' })
  @IsString()
  service_type: string;

  @ApiPropertyOptional({ description: 'Service ID reference' })
  @IsOptional()
  @IsString()
  service_id?: string;

  @ApiPropertyOptional({ description: 'User ID for authenticated users' })
  @IsOptional()
  @IsString()
  user_id?: string;

  @ApiPropertyOptional({
    description: 'Guest name for non-authenticated users',
  })
  @IsOptional()
  @IsString()
  guest_name?: string;

  @ApiPropertyOptional({
    description: 'Guest email for non-authenticated users',
  })
  @IsOptional()
  @IsString()
  guest_email?: string;

  @ApiPropertyOptional({
    description: 'Guest mobile for non-authenticated users',
  })
  @IsOptional()
  @IsString()
  guest_mobile?: string;

  @ApiPropertyOptional({
    description: 'Application priority level',
    enum: PriorityLevel,
  })
  @IsOptional()
  @IsEnum(PriorityLevel)
  priority_level?: PriorityLevel;

  // REMOVED: metadata field - following non-destructive patterns
  // @ApiPropertyOptional({ description: 'Additional metadata' })
  // @IsOptional()
  // @IsObject()
  // metadata?: any;
}

/**
 * DTO for creating a new application via POST /applications endpoint
 * Matches the exact JSON structure specified in requirements
 */
export class CreateNewApplicationDto {
  @ApiProperty({
    description: 'Service type identifier (e.g., "immigration")',
    example: 'immigration'
  })
  @IsString()
  service_type: string;

  @ApiProperty({
    description: 'Service ID reference',
    example: 'service_123'
  })
  @IsString()
  service_id: string;

  @ApiProperty({
    description: 'User ID for the application',
    example: 'user_456'
  })
  @IsString()
  user_id: string;

  @ApiProperty({
    description: 'Priority level for the application',
    enum: PriorityLevel,
    example: 'Medium'
  })
  @IsEnum(PriorityLevel)
  priority_level: PriorityLevel;

  @ApiProperty({
    description: 'Workflow template ID',
    example: 'template_789'
  })
  @IsString()
  workflow_template_id: string;

  @ApiProperty({
    description: 'Array of payment IDs associated with this application',
    type: [String],
    example: ['payment_1', 'payment_2']
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  payments: string[];

  @ApiProperty({
    description: 'Array of assigned agent IDs',
    type: [String],
    example: ['agent_1', 'agent_2']
  })
  @IsArray()
  @IsOptional()
  @ArrayNotEmpty()
  @IsString({ each: true })
  assigned_agent: string[];
}

/**
 * DTO for updating application status
 */
export class UpdateApplicationStatusDto {
  @ApiProperty({
    description: 'New application status',
    enum: ApplicationStatus,
  })
  @IsEnum(ApplicationStatus)
  status: ApplicationStatus;

  @ApiPropertyOptional({ description: 'Assigned user ID' })
  @IsOptional()
  @IsString()
  assigned_to?: string;

  // REMOVED: sla_deadline field - following non-destructive patterns
  // @ApiPropertyOptional({ description: 'SLA deadline' })
  // @IsOptional()
  // @IsDateString()
  // sla_deadline?: string;

  @ApiPropertyOptional({ description: 'Estimated completion date' })
  @IsOptional()
  @IsDateString()
  estimated_completion?: string;
}

/**
 * DTO for updating application step status
 */
export class UpdateStepStatusDto {
  @ApiProperty({ description: 'New step status', enum: WorkflowStepStatus })
  @IsEnum(WorkflowStepStatus)
  status: WorkflowStepStatus;

  @ApiPropertyOptional({ description: 'Step data' })
  @IsOptional()
  @IsObject()
  step_data?: any;

  @ApiPropertyOptional({ description: 'Assigned user ID' })
  @IsOptional()
  @IsString()
  assigned_to?: string;

  @ApiPropertyOptional({ description: 'Reviewer ID' })
  @IsOptional()
  @IsString()
  reviewer_id?: string;

  @ApiPropertyOptional({ description: 'Review notes' })
  @IsOptional()
  @IsString()
  review_notes?: string;

  @ApiPropertyOptional({ description: 'Attachments' })
  @IsOptional()
  @IsObject()
  attachments?: any;
}

/**
 * DTO for creating workflow templates
 */
export class CreateWorkflowTemplateDto {
  @ApiProperty({ description: 'Template name' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Template description' })
  @IsOptional()
  @IsString()
  description?: string;

  // REMOVED: application_type field - following non-destructive patterns
  // @ApiProperty({ description: 'Application type this template applies to' })
  // @IsString()
  // application_type: string;

  @ApiPropertyOptional({ description: 'Specific service type filter' })
  @IsOptional()
  @IsString()
  service_type?: string;

  @ApiProperty({ description: 'Steps configuration as JSON' })
  @IsObject()
  steps_configuration: any;

  @ApiPropertyOptional({ description: 'Estimated duration in days' })
  @IsOptional()
  @IsNumber()
  estimated_duration?: number;

  @ApiPropertyOptional({ description: 'SLA threshold in days' })
  @IsOptional()
  @IsNumber()
  sla_threshold?: number;

  @ApiPropertyOptional({ description: 'Template version' })
  @IsOptional()
  @IsString()
  version?: string;
}

/**
 * DTO for application filtering and search
 */
export class ApplicationFiltersDto {
  @ApiPropertyOptional({ description: 'Filter by service type' })
  @IsOptional()
  @IsString()
  service_type?: string;

  @ApiPropertyOptional({
    description: 'Filter by status',
    enum: ApplicationStatus,
  })
  @IsOptional()
  @IsEnum(ApplicationStatus)
  status?: ApplicationStatus;

  @ApiPropertyOptional({ description: 'Filter by user ID' })
  @IsOptional()
  @IsString()
  user_id?: string;

  @ApiPropertyOptional({ description: 'Filter by assigned user' })
  @IsOptional()
  @IsString()
  assigned_to?: string;

  @ApiPropertyOptional({
    description: 'Filter by priority level',
    enum: PriorityLevel,
  })
  @IsOptional()
  @IsEnum(PriorityLevel)
  priority_level?: PriorityLevel;

  @ApiPropertyOptional({ description: 'Filter by creation date from' })
  @IsOptional()
  @IsDateString()
  created_from?: string;

  @ApiPropertyOptional({ description: 'Filter by creation date to' })
  @IsOptional()
  @IsDateString()
  created_to?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    default: 1,
    minimum: 1,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return 1;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? 1 : parsed;
  })
  @IsInt()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({
    description: 'Items per page',
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return 10;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? 10 : parsed;
  })
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number;
}

/**
 * DTO for application response
 */
export class ApplicationResponseDto {
  @ApiProperty({ description: 'Application ID' })
  id: string;

  @ApiProperty({ description: 'Application number' })
  application_number: string;

  // REMOVED: application_type field - following non-destructive patterns
  // @ApiProperty({ description: 'Application type' })
  // application_type: string;

  @ApiProperty({ description: 'Service type' })
  service_type: string;

  @ApiProperty({ description: 'Application status', enum: ApplicationStatus })
  status: ApplicationStatus;

  @ApiProperty({ description: 'Current workflow step number', example: 1 })
  current_step: number;

  @ApiProperty({ description: 'Priority level', enum: PriorityLevel })
  priority_level: PriorityLevel;

  @ApiPropertyOptional({ description: 'User information' })
  user?: {
    id: string;
    name: string;
    email: string;
  };

  @ApiPropertyOptional({ description: 'Guest information' })
  guest?: {
    name: string;
    email: string;
    mobile: string;
  };

  // REMOVED: current_step object - replaced with current_step integer field
  // @ApiPropertyOptional({ description: 'Current step information' })
  // current_step?: {
  //   id: string;
  //   step_name: string;
  //   status: WorkflowStepStatus;
  //   due_date?: Date;
  // };

  @ApiProperty({ description: 'Application steps' })
  steps: Array<{
    id: string;
    step_name: string;
    step_order: number;
    status: WorkflowStepStatus;
    due_date?: Date;
  }>;

  @ApiPropertyOptional({ description: 'Workflow template' })
  workflow_template?: {
    id: string;
    name: string;
    description: string;
    workflowTemplate: Array<any>;
  };

  // REMOVED: sla_deadline field - following non-destructive patterns
  // @ApiPropertyOptional({ description: 'SLA deadline' })
  // workflow_template?: Date;

  @ApiPropertyOptional({ description: 'Estimated completion' })
  estimated_completion?: Date;

  @ApiPropertyOptional({ description: 'Application notes' })
  note?: string;

  @ApiProperty({ description: 'Creation date' })
  created_at: Date;

  @ApiProperty({ description: 'Last update date' })
  updated_at: Date;
}

/**
 * DTO for paginated application list response
 */
export class PaginatedApplicationResponseDto {
  @ApiProperty({
    description: 'List of applications',
    type: [ApplicationResponseDto],
  })
  data: ApplicationResponseDto[];

  @ApiProperty({ description: 'Total count of applications' })
  total: number;

  @ApiProperty({ description: 'Current page number' })
  page: number;

  @ApiProperty({ description: 'Items per page' })
  limit: number;

  @ApiProperty({ description: 'Total number of pages' })
  totalPages: number;
}

/**
 * DTO for new application creation response
 */
export class CreateApplicationResponseDto {
  @ApiProperty({ description: 'Success status' })
  success: boolean;

  @ApiProperty({ description: 'Response message' })
  message: string;

  @ApiProperty({ description: 'Created application data' })
  data: {
    id: string;
    application_number: string;
    service_type: string;
    service_id: string;
    user_id: string;
    priority_level: PriorityLevel;
    workflow_template_id: string;
    payment_ids: string[];
    agent_ids: string[];
    status: ApplicationStatus;
    current_step: string;
    created_at: Date;
    updated_at: Date;
  };
}

/**
 * DTO for document upload
 */
export class DocumentUploadDto {
  @ApiProperty({ description: 'Document name' })
  @IsString()
  document_name: string;

  @ApiProperty({ description: 'Document type' })
  @IsString()
  document_type: string;

  @ApiPropertyOptional({ description: 'Document category' })
  @IsOptional()
  @IsString()
  document_category?: string;

  @ApiPropertyOptional({ description: 'Expiry date' })
  @IsOptional()
  @IsDateString()
  expiry_date?: string;

  @ApiPropertyOptional({ description: 'Document tags', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: 'Application ID to link document' })
  @IsOptional()
  @IsString()
  application_id?: string;
}

/**
 * DTO for document update
 */
export class DocumentUpdateDto {
  @ApiPropertyOptional({ description: 'Document name' })
  @IsOptional()
  @IsString()
  document_name?: string;

  @ApiPropertyOptional({ description: 'Document category' })
  @IsOptional()
  @IsString()
  document_category?: string;

  @ApiPropertyOptional({ description: 'Expiry date' })
  @IsOptional()
  @IsDateString()
  expiry_date?: string;

  @ApiPropertyOptional({ description: 'Document tags', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * DTO for document search
 */
export class DocumentSearchDto {
  @ApiProperty({ description: 'Search query' })
  @IsString()
  query: string;

  @ApiPropertyOptional({ description: 'Document type filter' })
  @IsOptional()
  @IsString()
  document_type?: string;

  @ApiPropertyOptional({ description: 'Document status filter' })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional({ description: 'Tags filter', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: 'Date from filter' })
  @IsOptional()
  @IsDateString()
  date_from?: string;

  @ApiPropertyOptional({ description: 'Date to filter' })
  @IsOptional()
  @IsDateString()
  date_to?: string;

  @ApiPropertyOptional({
    description: 'Page number',
    default: 1,
    minimum: 1,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return 1;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? 1 : parsed;
  })
  @IsInt()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({
    description: 'Items per page',
    default: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return 20;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? 20 : parsed;
  })
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Sort by field',
    enum: ['relevance', 'date', 'name', 'size'],
  })
  @IsOptional()
  @IsString()
  sort_by?: 'relevance' | 'date' | 'name' | 'size';

  @ApiPropertyOptional({ description: 'Sort order', enum: ['asc', 'desc'] })
  @IsOptional()
  @IsString()
  sort_order?: 'asc' | 'desc';

  @ApiPropertyOptional({
    description: 'Include content in search',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  include_content?: boolean;
}

/**
 * DTO for notification creation
 */
export class CreateNotificationDto {
  @ApiProperty({ description: 'Notification type' })
  @IsString()
  notification_type: string;

  @ApiProperty({ description: 'Recipient email' })
  @IsString()
  recipient_email: string;

  @ApiPropertyOptional({ description: 'Recipient mobile' })
  @IsOptional()
  @IsString()
  recipient_mobile?: string;

  @ApiPropertyOptional({ description: 'Message subject' })
  @IsOptional()
  @IsString()
  subject?: string;

  @ApiProperty({ description: 'Message body' })
  @IsString()
  message_body: string;

  @ApiPropertyOptional({ description: 'Schedule time' })
  @IsOptional()
  @IsDateString()
  scheduled_at?: string;

  @ApiPropertyOptional({ description: 'Application ID reference' })
  @IsOptional()
  @IsString()
  application_id?: string;

  // REMOVED: metadata field - following non-destructive patterns
  // @ApiPropertyOptional({ description: 'Additional metadata' })
  // @IsOptional()
  // @IsObject()
  // metadata?: any;
}
