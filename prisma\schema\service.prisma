model packages {
  id        String          @id @default(cuid())
  name      String
  note      String
  amount    Int
  service   String[]
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt
  order     Int?
  guest     guest_package[]
  payments  payment[]
  users     user_package[]
}

model immigration_service {
  id        String                      @id @default(cuid())
  name      String
  amount    Int
  service   String[]
  createdAt DateTime                    @default(now())
  updatedAt DateTime                    @updatedAt
  order     Int?
  guest     guest_immigration_service[]
  payments  payment[]
  users     user_immigration_service[]
}

model training {
  id         String           @id @default(cuid())
  name       String
  amount     Int
  service    String[]
  highlights String[]
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt
  order      Int?
  img        String
  guest      guest_training[]
  payments   payment[]
  users      user_training[]
}
